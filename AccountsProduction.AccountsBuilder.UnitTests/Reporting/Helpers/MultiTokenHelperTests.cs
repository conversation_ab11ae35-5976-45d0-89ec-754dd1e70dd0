﻿using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Application.Reporting.Helpers;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using AccountsProduction.AccountsBuilder.Reporting.Application.Dto;
using AccountsProduction.AccountsBuilder.UnitTests.Reporting.StaticData;
using Iris.AccountsProduction.AccountsBuilder.Messages.FinancialData;
using Iris.AccountsProduction.AccountsBuilder.Messages.ProfitShare;
using Xunit;

namespace AccountsProduction.AccountsBuilder.UnitTests.Reporting.Helpers;

public class MultiTokenHelperTests
{
    [Fact]
    public void GetNumbersForTokenType_ShouldReturnCorrectNumbers()
    {
        var result = MultiTokenHelper.GetNumbersForTokenType(TokenType.SocieLimitedCompany, "Limited");
        Assert.Equal(new List<int> { 966, 969, 968, 970, 971, 972, 973, 974 }, result);

        result = MultiTokenHelper.GetNumbersForTokenType(TokenType.TangibleFixedAssets, "Limited");
        Assert.Equal(new List<int> { 512, 513, 514, 522, 523, 524, 525, 526 }, result);

        result = MultiTokenHelper.GetNumbersForTokenType(TokenType.FixedAssetInvestmentsPart1, "Limited");
        Assert.Equal(new List<int> { 542, 548, 549, 550, 556, 557, 561 }, result);

        result = MultiTokenHelper.GetNumbersForTokenType(TokenType.FixedAssetInvestmentsPart1, "LLP");
        Assert.Equal(new List<int> { 542, 548, 549, 550, 556, 557 }, result);

        result = MultiTokenHelper.GetNumbersForTokenType(TokenType.FixedAssetInvestmentsPart1, "IncorporatedCharity");
        Assert.Equal(new List<int> { 542, 556, 557, 561, 564, 566, 567 }, result);

        result = MultiTokenHelper.GetNumbersForTokenType(TokenType.FixedAssetInvestmentsPart1, "UnincorporatedCharity");
        Assert.Equal(new List<int> { 542, 556, 557, 561, 564, 566, 567 }, result);

        result = MultiTokenHelper.GetNumbersForTokenType(TokenType.FixedAssetInvestmentsPart2, "Limited");
        Assert.Equal(new List<int> { 543, 551, 552, 553, 559 }, result);

        result = MultiTokenHelper.GetNumbersForTokenType(TokenType.RomiLlp, "LLP");
        Assert.Equal(new List<int> { 975, 976, 977, 978, 970, 972, 973, 974, 999, 985, 987, 980, 981, 997, 998, 989, 990 }, result);

        result = MultiTokenHelper.GetNumbersForTokenType(TokenType.Investments, "Limited");
        Assert.Equal(new List<int> { 542, 548, 549, 550, 556, 557, 561 }, result);

        result = MultiTokenHelper.GetNumbersForTokenType(TokenType.LoansOthFinAssets, "Limited");
        Assert.Equal(new List<int> { 543, 551, 552, 553, 559 }, result);

        result = MultiTokenHelper.GetNumbersForTokenType(TokenType.TangibleFixedAssetsROU, "Limited");
        Assert.Equal(new List<int> { 512, 513, 514, 522, 523, 524, 525, 526 }, result);
    }

    [Fact]
    public void GetTokenPatternForTokenType_ShouldReturnCorrectPatterns()
    {
        var result = MultiTokenHelper.GetTokenPatternForTokenType(TokenType.SocieLimitedCompany, "Limited");
        Assert.NotNull(result);
        Assert.True(result.ContainsKey(8));
        Assert.False(result.ContainsKey(9));

        result = MultiTokenHelper.GetTokenPatternForTokenType(TokenType.TangibleFixedAssets, "Limited");
        Assert.NotNull(result);
        Assert.True(result.ContainsKey(8));
        Assert.False(result.ContainsKey(9));

        result = MultiTokenHelper.GetTokenPatternForTokenType(TokenType.FixedAssetInvestmentsPart1, "Limited");
        Assert.NotNull(result);
        Assert.True(result.ContainsKey(7));
        Assert.False(result.ContainsKey(8));

        result = MultiTokenHelper.GetTokenPatternForTokenType(TokenType.FixedAssetInvestmentsPart1, "LLP");
        Assert.NotNull(result);
        Assert.True(result.ContainsKey(6));
        Assert.False(result.ContainsKey(7));

        result = MultiTokenHelper.GetTokenPatternForTokenType(TokenType.FixedAssetInvestmentsPart1, "IncorporatedCharity");
        Assert.NotNull(result);
        Assert.True(result.ContainsKey(7));
        Assert.False(result.ContainsKey(8));

        result = MultiTokenHelper.GetTokenPatternForTokenType(TokenType.FixedAssetInvestmentsPart1, "UnincorporatedCharity");
        Assert.NotNull(result);
        Assert.True(result.ContainsKey(7));
        Assert.False(result.ContainsKey(8));

        result = MultiTokenHelper.GetTokenPatternForTokenType(TokenType.FixedAssetInvestmentsPart2, "Limited");
        Assert.NotNull(result);
        Assert.True(result.ContainsKey(5));
        Assert.False(result.ContainsKey(6));

        result = MultiTokenHelper.GetTokenPatternForTokenType(TokenType.Investments, "Limited");
        Assert.NotNull(result);
        Assert.True(result.ContainsKey(7));
        Assert.False(result.ContainsKey(8));

        result = MultiTokenHelper.GetTokenPatternForTokenType(TokenType.LoansOthFinAssets, "Limited");
        Assert.NotNull(result);
        Assert.True(result.ContainsKey(5));
        Assert.False(result.ContainsKey(6));

        result = MultiTokenHelper.GetTokenPatternForTokenType(TokenType.TangibleFixedAssetsROU, "Limited");
        Assert.NotNull(result);
        Assert.True(result.ContainsKey(8));
        Assert.False(result.ContainsKey(9));
    }

    [Theory]
    [InlineData("FRS102", BusinessTypes.LimitedBusinessType, TokenType.SocieLimitedCompany, true)]
    [InlineData("FRS102", BusinessTypes.LlpBusinessType, TokenType.SocieLimitedCompany, false)]
    [InlineData("FRS102", BusinessTypes.LimitedBusinessType, TokenType.TangibleFixedAssets, true)]
    [InlineData("FRS102", BusinessTypes.LlpBusinessType, TokenType.TangibleFixedAssets, true)]
    [InlineData("FRS102", BusinessTypes.LimitedBusinessType, TokenType.FixedAssetInvestmentsPart1, true)]
    [InlineData("FRS102", BusinessTypes.LlpBusinessType, TokenType.FixedAssetInvestmentsPart1, true)]
    [InlineData("Charities SORP - FRS102", BusinessTypes.IncorporatedCharityBusinessType, TokenType.FixedAssetInvestmentsPart1, true)]
    [InlineData("Charities SORP - FRS102", BusinessTypes.UnincorporatedCharityBusinessType, TokenType.FixedAssetInvestmentsPart1, true)]
    [InlineData("Charities SORP - FRS102", BusinessTypes.IncorporatedCharityBusinessType, TokenType.TangibleFixedAssets, true)]
    [InlineData("Charities SORP - FRS102", BusinessTypes.UnincorporatedCharityBusinessType, TokenType.TangibleFixedAssets, true)]
    [InlineData("FRS102", BusinessTypes.LimitedBusinessType, TokenType.FixedAssetInvestmentsPart2, true)]
    [InlineData("FRS102", BusinessTypes.LlpBusinessType, TokenType.FixedAssetInvestmentsPart2, true)]
    [InlineData("Charities SORP - FRS102", BusinessTypes.IncorporatedCharityBusinessType, TokenType.FixedAssetInvestmentsPart2, true)]
    [InlineData("Charities SORP - FRS102", BusinessTypes.UnincorporatedCharityBusinessType, TokenType.FixedAssetInvestmentsPart2, true)]
    [InlineData("FRS102", BusinessTypes.LlpBusinessType, TokenType.RomiLlp, true)]
    [InlineData("FRS102", BusinessTypes.LimitedBusinessType, TokenType.RomiLlp, false)]
    [InlineData("FRS105", BusinessTypes.LlpBusinessType, TokenType.SocieLimitedCompany, false)]
    [InlineData("IFRS", BusinessTypes.LimitedBusinessType, TokenType.LoansOthFinAssets, true)]
    [InlineData("IFRS", BusinessTypes.LimitedBusinessType, TokenType.TangibleFixedAssets, true)]
    [InlineData("IFRS", BusinessTypes.LimitedBusinessType, TokenType.Investments, true)]
    [InlineData("IFRS", BusinessTypes.LimitedBusinessType, TokenType.BorrowingsMaturityCY, true)]
    [InlineData("IFRS", BusinessTypes.LimitedBusinessType, TokenType.SocieLimitedCompany, true)]
    [InlineData("IFRS", BusinessTypes.LimitedBusinessType, TokenType.TangibleFixedAssetsROU, true)]
    public void IsMultiTokenEligible_ShouldReturnExpectedResult(string reportType, string businessType, TokenType tokenType, bool expectedResult)
    {
        // Act
        var result = MultiTokenHelper.IsMultiTokenEligible(reportType, businessType, tokenType);

        // Assert
        Assert.Equal(expectedResult, result);
    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_Limited_ShouldReturnMultiColumnToken()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        var socieLimitedCompany = result.First(x => x.AssignedToken == TokenType.SocieLimitedCompany.ToString());
        var tangibleFixedAssets = result.First(x => x.AssignedToken == TokenType.TangibleFixedAssets.ToString());
        var fixedAssetInvestmentsPart1 = result.First(x => x.AssignedToken == TokenType.FixedAssetInvestmentsPart1.ToString());
        var fixedAssetInvestmentsPart2 = result.First(x => x.AssignedToken == TokenType.FixedAssetInvestmentsPart2.ToString());

        Assert.Equal(data.ClientId, socieLimitedCompany.ClientId);
        Assert.Equal(currentPeriod!.Id, socieLimitedCompany.AccountPeriodId);
        Assert.Equal("SocieLimitedCompany", socieLimitedCompany.AssignedToken);
        Assert.Equal("969", socieLimitedCompany.Token2);
        Assert.Null(socieLimitedCompany.Token5);
        Assert.Equal("Total", socieLimitedCompany.Token9);
        Assert.Equal(7, socieLimitedCompany.TokenCount);

        Assert.Equal(data.ClientId, tangibleFixedAssets.ClientId);
        Assert.Equal(currentPeriod!.Id, tangibleFixedAssets.AccountPeriodId);
        Assert.Equal("TangibleFixedAssets", tangibleFixedAssets.AssignedToken);
        Assert.Null(tangibleFixedAssets.Token4);
        Assert.Equal("512", tangibleFixedAssets.Token5);
        Assert.Equal("Total", tangibleFixedAssets.Token9);
        Assert.Equal(4, tangibleFixedAssets.TokenCount);

        Assert.Equal(data.ClientId, fixedAssetInvestmentsPart1.ClientId);
        Assert.Equal(currentPeriod!.Id, fixedAssetInvestmentsPart1.AccountPeriodId);
        Assert.Equal("FixedAssetInvestmentsPart1", fixedAssetInvestmentsPart1.AssignedToken);
        Assert.Equal("542", fixedAssetInvestmentsPart1.Token3);
        Assert.Null(fixedAssetInvestmentsPart1.Token5);
        Assert.Equal("Total", fixedAssetInvestmentsPart1.Token8);
        Assert.Equal(4, fixedAssetInvestmentsPart1.TokenCount);

        Assert.Equal(data.ClientId, fixedAssetInvestmentsPart2.ClientId);
        Assert.Equal(currentPeriod!.Id, fixedAssetInvestmentsPart2.AccountPeriodId);
        Assert.Equal("FixedAssetInvestmentsPart2", fixedAssetInvestmentsPart2.AssignedToken);
        Assert.Null(fixedAssetInvestmentsPart2.Token1);
        Assert.Equal("551", fixedAssetInvestmentsPart2.Token2);
        Assert.Null(fixedAssetInvestmentsPart2.Token4);
        Assert.Equal("Total", fixedAssetInvestmentsPart2.Token6);
        Assert.Equal(3, fixedAssetInvestmentsPart2.TokenCount);
    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_LLP_ShouldReturnMultiColumnToken()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ClientData!.CompanyType = "LLP";
        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        var tangibleFixedAssets = result.First(x => x.AssignedToken == TokenType.TangibleFixedAssets.ToString());
        var fixedAssetInvestmentsPart1 = result.First(x => x.AssignedToken == TokenType.FixedAssetInvestmentsPart1.ToString());
        var romiLlp = result.First(x => x.AssignedToken == TokenType.RomiLlp.ToString());

        Assert.Equal(data.ClientId, tangibleFixedAssets.ClientId);
        Assert.Equal(currentPeriod!.Id, tangibleFixedAssets.AccountPeriodId);
        Assert.Equal("TangibleFixedAssets", tangibleFixedAssets.AssignedToken);
        Assert.Equal("512", tangibleFixedAssets.Token5);
        Assert.Equal("Total", tangibleFixedAssets.Token9);
        Assert.Equal(4, tangibleFixedAssets.TokenCount);

        Assert.Equal(data.ClientId, fixedAssetInvestmentsPart1.ClientId);
        Assert.Equal(currentPeriod!.Id, fixedAssetInvestmentsPart1.AccountPeriodId);
        Assert.Equal("FixedAssetInvestmentsPart1", fixedAssetInvestmentsPart1.AssignedToken);
        Assert.Equal("542", fixedAssetInvestmentsPart1.Token3);
        Assert.Null(fixedAssetInvestmentsPart1.Token5);
        Assert.Equal("Total", fixedAssetInvestmentsPart1.Token8);
        Assert.Equal(4, fixedAssetInvestmentsPart1.TokenCount);

        Assert.Equal(data.ClientId, romiLlp.ClientId);
        Assert.Equal(currentPeriod!.Id, romiLlp.AccountPeriodId);
        Assert.Equal("RomiLlp", romiLlp.AssignedToken);
        Assert.Equal("EQ B", romiLlp.Token3);
        Assert.Equal("EQ C", romiLlp.Token4);
        Assert.Equal("EQ TOT", romiLlp.Token5);
        Assert.Null(romiLlp.Token6);
        Assert.Equal("DB A", romiLlp.Token7);
        Assert.Equal("DB B", romiLlp.Token8);
        Assert.Equal("DB TOT", romiLlp.Token9);
        Assert.Equal(6, romiLlp.TokenCount);
    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_Should_ReturnSOCIEToken_Including_ProfitLossReserveData()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.TrialBalanceData = Data.GetPeriodTrialBalanceDataSOCIEAccountCodes();
        data.FinancialData = new FinancialDataDto
        {
            Data = new List<FinancialDto>
            {
                new FinancialDto
                {
                    ProfitLossReserve = new FinancialDataCategoryMessage
                    {
                        Value = "1000",
                    }
                }
            }
        };
        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        var socieLimitedCompany = result.First(x => x.AssignedToken == TokenType.SocieLimitedCompany.ToString());

        Assert.Equal(data.ClientId, socieLimitedCompany.ClientId);
        Assert.Equal(currentPeriod!.Id, socieLimitedCompany.AccountPeriodId);
        Assert.Equal("SocieLimitedCompany", socieLimitedCompany.AssignedToken);
        Assert.Equal("966", socieLimitedCompany.Token2);
        Assert.Equal("969", socieLimitedCompany.Token3);
        Assert.Equal("968", socieLimitedCompany.Token4);
        Assert.Null(socieLimitedCompany.Token5);
        Assert.Null(socieLimitedCompany.Token6);
        Assert.Equal("970", socieLimitedCompany.Token7);
        Assert.Equal("971", socieLimitedCompany.Token8);
        Assert.Equal("Total", socieLimitedCompany.Token9);
        Assert.Equal(5, socieLimitedCompany.TokenCount);
    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_Should_ReturnRomiLLPToken_Including_ProfitShareData()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ClientData!.CompanyType = "LLP";
        data.TrialBalanceData = new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
            {
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 1,
                    Amount = 100,
                    PeriodId = TestHelpers.Guids.GuidTwo
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 970,
                    Amount = 100,
                    PeriodId = TestHelpers.Guids.GuidTwo
                },
                new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
                {
                    AccountCode = 990,
                    Amount = 100,
                    PeriodId = TestHelpers.Guids.GuidOne
                }
            };
        data.ProfitShareData = new ProfitShareDataDto
        {
            UnallocatedAmount = 1000,

            ProfitShares = new List<ProfitShareMessage>
            {
                new ProfitShareMessage
                {
                    AccountPeriodId = Guid.NewGuid(),
                    InvolvementId = 12345,
                    CumulativeAmount = 1000
                }
            }
        };
        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        var romiLlp = result.First(x => x.AssignedToken == TokenType.RomiLlp.ToString());

        Assert.Equal(data.ClientId, romiLlp.ClientId);
        Assert.Equal(currentPeriod!.Id, romiLlp.AccountPeriodId);
        Assert.Equal("RomiLlp", romiLlp.AssignedToken);
        Assert.Equal("EQ B", romiLlp.Token3);
        Assert.Equal("EQ C", romiLlp.Token4);
        Assert.Equal("EQ TOT", romiLlp.Token5);
        Assert.Null(romiLlp.Token6);
        Assert.Equal("DB A", romiLlp.Token7);
        Assert.Equal("DB B", romiLlp.Token8);
        Assert.Equal("DB TOT", romiLlp.Token9);
        Assert.Equal(6, romiLlp.TokenCount);
    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_Should_ReturnRomiLLPToken_With_Or_Without_Equity_Or_Debt()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ClientData!.CompanyType = "LLP";
        data.TrialBalanceData = new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
        {
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 970,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            }
        };

        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        var romiLlp = result.First(x => x.AssignedToken == TokenType.RomiLlp.ToString());

        Assert.Equal(data.ClientId, romiLlp.ClientId);
        Assert.Equal(currentPeriod!.Id, romiLlp.AccountPeriodId);
        Assert.Equal("RomiLlp", romiLlp.AssignedToken);
        Assert.Null(romiLlp.Token3);
        Assert.Null(romiLlp.Token4);
        Assert.Null(romiLlp.Token5);
        Assert.Null(romiLlp.Token6);
        Assert.Null(romiLlp.Token7);
        Assert.Equal("EQ B", romiLlp.Token8);
        Assert.Equal("EQ TOT", romiLlp.Token9);
        Assert.Equal(2, romiLlp.TokenCount);

        data.TrialBalanceData = new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
        {
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 985,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            }
        };

        result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        romiLlp = result.First(x => x.AssignedToken == TokenType.RomiLlp.ToString());

        Assert.Equal(data.ClientId, romiLlp.ClientId);
        Assert.Equal(currentPeriod!.Id, romiLlp.AccountPeriodId);
        Assert.Equal("RomiLlp", romiLlp.AssignedToken);
        Assert.Null(romiLlp.Token3);
        Assert.Null(romiLlp.Token4);
        Assert.Null(romiLlp.Token5);
        Assert.Null(romiLlp.Token6);
        Assert.Null(romiLlp.Token7);
        Assert.Equal("DB A", romiLlp.Token8);
        Assert.Equal("DB TOT", romiLlp.Token9);
        Assert.Equal(2, romiLlp.TokenCount);
    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_Should_ReturnRomiLLPToken_With_Each_Equity_And_Debt()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ClientData!.CompanyType = "LLP";
        data.TrialBalanceData = new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
        {
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 970,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 990,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            }
        };

        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        var romiLlp = result.First(x => x.AssignedToken == TokenType.RomiLlp.ToString());

        Assert.Equal(data.ClientId, romiLlp.ClientId);
        Assert.Equal(currentPeriod!.Id, romiLlp.AccountPeriodId);
        Assert.Equal("RomiLlp", romiLlp.AssignedToken);
        Assert.Null(romiLlp.Token3);
        Assert.Null(romiLlp.Token4);
        Assert.Null(romiLlp.Token5);
        Assert.Equal("EQ B", romiLlp.Token6);
        Assert.Equal("EQ TOT", romiLlp.Token7);
        Assert.Equal("DB B", romiLlp.Token8);
        Assert.Equal("DB TOT", romiLlp.Token9);
        Assert.Equal(4, romiLlp.TokenCount);
    }

    [Theory]
    [InlineData("IncorporatedCharity")]
    [InlineData("UnincorporatedCharity")]
    public async Task GetProcessedMultiColumnToken_Should_ReturnFAIPart1_With_Charity(string companyType)
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ReportType = "Charities SORP - FRS102";
        data.ClientData!.CompanyType = companyType;
        data.TrialBalanceData = new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
        {
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 542,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 556,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 557,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 561,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            }
        };

        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        var fixedAssetInvestmentsPart1 = result.First(x => x.AssignedToken == TokenType.FixedAssetInvestmentsPart1.ToString());

        Assert.Equal(data.ClientId, fixedAssetInvestmentsPart1.ClientId);
        Assert.Equal(currentPeriod!.Id, fixedAssetInvestmentsPart1.AccountPeriodId);
        Assert.Equal("FixedAssetInvestmentsPart1", fixedAssetInvestmentsPart1.AssignedToken);
        Assert.Equal("542", fixedAssetInvestmentsPart1.Token3);
        Assert.Equal("556", fixedAssetInvestmentsPart1.Token4);
        Assert.Null(fixedAssetInvestmentsPart1.Token5);
        Assert.Equal("557", fixedAssetInvestmentsPart1.Token6);
        Assert.Equal("561", fixedAssetInvestmentsPart1.Token7);
        Assert.Equal("Total", fixedAssetInvestmentsPart1.Token8);
        Assert.Null(fixedAssetInvestmentsPart1.Token9);
        Assert.Equal(4, fixedAssetInvestmentsPart1.TokenCount);
    }

    [Theory]
    [InlineData("IncorporatedCharity")]
    [InlineData("UnincorporatedCharity")]
    public async Task GetProcessedMultiColumnToken_Should_ReturnFAIPart2_With_Charity(string companyType)
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ReportType = "Charities SORP - FRS102";
        data.ClientData!.CompanyType = companyType;
        data.TrialBalanceData = new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
        {
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 559,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 565,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            }
        };

        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        var fixedAssetInvestmentsPart2 = result.First(x => x.AssignedToken == TokenType.FixedAssetInvestmentsPart2.ToString());

        Assert.Equal(data.ClientId, fixedAssetInvestmentsPart2.ClientId);
        Assert.Equal(currentPeriod!.Id, fixedAssetInvestmentsPart2.AccountPeriodId);
        Assert.Equal("FixedAssetInvestmentsPart2", fixedAssetInvestmentsPart2.AssignedToken);
        Assert.Null(fixedAssetInvestmentsPart2.Token1);
        Assert.Equal("559", fixedAssetInvestmentsPart2.Token2);
        Assert.Equal("565", fixedAssetInvestmentsPart2.Token3);
        Assert.Equal("Total", fixedAssetInvestmentsPart2.Token4);
        Assert.Null(fixedAssetInvestmentsPart2.Token5);
        Assert.Equal(2, fixedAssetInvestmentsPart2.TokenCount);
    }

    [Theory]
    [InlineData("IncorporatedCharity")]
    [InlineData("UnincorporatedCharity")]
    public async Task GetProcessedMultiColumnToken_Should_Return_TangibleFixedAsset_With_Charity(string companyType)
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ReportType = "Charities SORP - FRS102";
        data.ClientData!.CompanyType = companyType;
        data.TrialBalanceData = new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
        {
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 512,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 513,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 514,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 522,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 523,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidTwo
            }
        };

        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        var tanngigleFixAssets = result.First(x => x.AssignedToken == TokenType.TangibleFixedAssets.ToString());

        Assert.Equal(data.ClientId, tanngigleFixAssets.ClientId);
        Assert.Equal(currentPeriod!.Id, tanngigleFixAssets.AccountPeriodId);
        Assert.Equal("TangibleFixedAssets", tanngigleFixAssets.AssignedToken);
        Assert.Equal("512", tanngigleFixAssets.Token2);
        Assert.Equal("513", tanngigleFixAssets.Token3);
        Assert.Equal("514", tanngigleFixAssets.Token4);
        Assert.Null(tanngigleFixAssets.Token5);
        Assert.Null(tanngigleFixAssets.Token6);
        Assert.Equal("522", tanngigleFixAssets.Token7);
        Assert.Equal("523", tanngigleFixAssets.Token8);
        Assert.Equal("Total", tanngigleFixAssets.Token9);
        Assert.Equal(5, tanngigleFixAssets.TokenCount);
    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_ShouldReturnEmptyListWithNoAccountCodeMatchingToken()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.TrialBalanceData = new List<PeriodTrialBalance>
        {
            new PeriodTrialBalance { AccountCode = 1, Amount = 100 }
        };

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());

        Assert.Empty(result);
    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_Should_Return_With_Investments()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ReportType = "IFRS";
        data.ClientData!.CompanyType = "Limited";
        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);
        var previousPeriod = data.ReportingPeriods.MinBy(x => x.EndDate);

        data.TrialBalanceData = new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
        {
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 542,
                Amount = 100,
                PeriodId = currentPeriod!.Id
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 548,
                Amount = 100,
                PeriodId = currentPeriod!.Id
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 549,
                Amount = 200,
                PeriodId = previousPeriod!.Id
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 550,
                Amount = 300,
                PeriodId = previousPeriod!.Id
            }
        };

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        var investments = result.First(x => x.AssignedToken == TokenType.Investments.ToString());

        Assert.Equal(data.ClientId, investments.ClientId);
        Assert.Equal(currentPeriod!.Id, investments.AccountPeriodId);
        Assert.Equal("Investments", investments.AssignedToken);
        Assert.Equal("542", investments.Token3);
        Assert.Equal("548", investments.Token4);
        Assert.Null(investments.Token5);
        Assert.Equal("549", investments.Token6);
        Assert.Equal("550", investments.Token7);
        Assert.Equal("Total", investments.Token8);
        Assert.Null(investments.Token9);
        Assert.Equal(4, investments.TokenCount);
    }
    [Fact]
    public async Task GetProcessedMultiColumnToken_Should_ReturnLoansOthFinAssets()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ReportType = "IFRS";
        data.ClientData!.CompanyType = "Limited";

        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate)!;

        data.TrialBalanceData = new List<PeriodTrialBalance>
    {
        new PeriodTrialBalance { AccountCode = 543, Amount = 100, PeriodId = currentPeriod.Id },
        new PeriodTrialBalance { AccountCode = 551, Amount = 100, PeriodId = currentPeriod.Id },
        new PeriodTrialBalance { AccountCode = 552, Amount = 100, PeriodId = currentPeriod.Id }
    };

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, CancellationToken.None);

        var loansToken = result.FirstOrDefault(x => x.AssignedToken == TokenType.LoansOthFinAssets.ToString());

        Assert.NotNull(loansToken);
        Assert.Equal(data.ClientId, loansToken.ClientId);
        Assert.Equal(currentPeriod.Id, loansToken.AccountPeriodId);
        Assert.Equal("LoansOthFinAssets", loansToken.AssignedToken);

        Assert.Equal("543", loansToken.Token2);
        Assert.Equal("551", loansToken.Token3);
        Assert.Equal("552", loansToken.Token5);
        Assert.Equal("Total", loansToken.Token6);
        Assert.Equal(3, loansToken.TokenCount);

    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_Should_Return_BorrowingsMaturityCY()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ReportType = "IFRS";
        data.ClientData!.CompanyType = BusinessTypes.LimitedBusinessType;
        data.TrialBalanceData = new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
    {
        new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
        {
            AccountCode = 839,
            Amount = 100,
            PeriodId = TestHelpers.Guids.GuidTwo
        },
        new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
        {
            AccountCode = 841,
            Amount = 200,
            PeriodId = TestHelpers.Guids.GuidTwo
        },
        new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
        {
            AccountCode = 843,
            Amount = 300,
            PeriodId = TestHelpers.Guids.GuidTwo
        },
        new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
        {
            AccountCode = 845,
            Amount = 400,
            PeriodId = TestHelpers.Guids.GuidTwo
        }
    };

        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());

        Assert.NotNull(result);
        var borrowings = result.First(x => x.AssignedToken == TokenType.BorrowingsMaturityCY.ToString());

        Assert.Equal(data.ClientId, borrowings.ClientId);
        Assert.Equal(currentPeriod!.Id, borrowings.AccountPeriodId);
        Assert.Equal("BorrowingsMaturityCY", borrowings.AssignedToken);
        Assert.Equal("MAT1", borrowings.Token1);
        Assert.Equal("MAT2", borrowings.Token2);
        Assert.Equal("MAT3", borrowings.Token3);
        Assert.Equal("MAT4", borrowings.Token4);
        Assert.Equal("Total", borrowings.Token5);
        Assert.Null(borrowings.Token6);
        Assert.Null(borrowings.Token7);
        Assert.Null(borrowings.Token8);
        Assert.Null(borrowings.Token9);
        Assert.Equal(4, borrowings.TokenCount);
    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_Should_Return_BorrowingsMaturityPY()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ReportType = "IFRS";
        data.ClientData!.CompanyType = BusinessTypes.LimitedBusinessType;

        // Add trial balance data for previous period only
        data.TrialBalanceData = new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
        {
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 839,
                Amount = 100,
                PeriodId = TestHelpers.Guids.GuidOne // Previous period
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 841,
                Amount = 200,
                PeriodId = TestHelpers.Guids.GuidOne // Previous period
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 843,
                Amount = 300,
                PeriodId = TestHelpers.Guids.GuidOne // Previous period
            }
        };

        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());

        Assert.NotNull(result);
        var borrowingsPY = result.First(x => x.AssignedToken == TokenType.BorrowingsMaturityPY.ToString());

        Assert.Equal(data.ClientId, borrowingsPY.ClientId);
        Assert.Equal(currentPeriod!.Id, borrowingsPY.AccountPeriodId);
        Assert.Equal("BorrowingsMaturityPY", borrowingsPY.AssignedToken);
        Assert.Equal("MAT1", borrowingsPY.Token2);
        Assert.Equal("MAT2", borrowingsPY.Token3);
        Assert.Equal("MAT3", borrowingsPY.Token4);
        Assert.Equal("Total", borrowingsPY.Token5);
        Assert.Null(borrowingsPY.Token1);
        Assert.Null(borrowingsPY.Token6);
        Assert.Null(borrowingsPY.Token7);
        Assert.Null(borrowingsPY.Token8);
        Assert.Null(borrowingsPY.Token9);
        Assert.Equal(3, borrowingsPY.TokenCount);
    }

    [Theory]
    [InlineData(910)]
    [InlineData(913)]
    [InlineData(916)]
    [InlineData(919)]
    [InlineData(922)]
    [InlineData(925)]
    public void GetSubAccountCodes_ReturnsExpectedList_ForValidAccountCodes(int accountCode)
    {
        // Arrange
        var reportType = ReportStandardType.IFRS;
        var businessType = BusinessTypes.LimitedBusinessType;
        var tokenType = TokenType.BorrowingsMaturityCY;

        // Act
        var result = MultiTokenHelper.GetSubAccountCodes(reportType, businessType, tokenType, accountCode);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(new List<int> { 1, 2, 3, 4 }, result);
    }

    [Fact]
    public async Task GetSubAccountCodes_ReturnsEmptyList_ForInvalidAccountCode()
    {
        // Arrange
        var reportType = ReportStandardType.IFRS;
        var businessType = BusinessTypes.LimitedBusinessType;
        var tokenType = TokenType.BorrowingsMaturityCY;
        var accountCode = 9999;

        // Act
        var result = MultiTokenHelper.GetSubAccountCodes(reportType, businessType, tokenType, accountCode);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Theory]
    [InlineData(new int[] { 839 }, null, new[] { null, null, null, null, "MAT1" }, 1)]
    [InlineData(new int[] { 841 }, null, new[] { null, null, null, null, "MAT2" }, 1)]
    [InlineData(new int[] { 843 }, null, new[] { null, null, null, null, "MAT3" }, 1)]
    [InlineData(new int[] { 845 }, null, new[] { null, null, null, null, "MAT4" }, 1)]
    [InlineData(new int[] { 839, 841, 843, 845 }, null, new[] { "MAT1", "MAT2", "MAT3", "MAT4", "Total" }, 4)]
    [InlineData(new int[] { 910 }, 1, new[] { null, null, null, null, "MAT2" }, 1)]
    [InlineData(new int[] { 910 }, 2, new[] { null, null, null, null, "MAT3" }, 1)]
    [InlineData(new int[] { 910, 913 }, 1, new[] { null, null, null, null, "MAT2" }, 1)]
    [InlineData(new int[] { 910, 913 }, 2, new[] { null, null, null, null, "MAT3" }, 1)]
    [InlineData(new int[] { 839, 841 }, null, new[] { null, null, "MAT1", "MAT2", "Total" }, 2)]
    [InlineData(new int[] { 839, 841, 843 }, null, new[] { null, "MAT1", "MAT2", "MAT3", "Total" }, 3)]
    [InlineData(new int[] { 839, 841, 843, 845, 846 }, null, new[] { "MAT1", "MAT2", "MAT3", "MAT4", "Total" }, 4)]
    public void GetOrderedTokenBorrowingsMaturityCY_ReturnsExpectedTokens(int[] codes, int? subAccount, string[] expectedTokens, int expectedCount)
    {
        var input = codes.Select(c => (c, subAccount)).ToList();
        var (count, tokens) = MultiTokenHelper.GetOrderedTokenBorrowingsMaturity(input);

        Assert.Equal(expectedCount, count);
        Assert.Equal(expectedTokens, tokens.ToArray());
    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_Should_ReturnTangibleFixedAssetsROU_For_IFRS_Limited()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ReportType = "IFRS";
        data.ClientData!.CompanyType = "Limited";
        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate)!;

        data.TrialBalanceData = new List<PeriodTrialBalance>
    {
        new PeriodTrialBalance { AccountCode = 512, SubAccountCode = 51, Amount = 100, PeriodId = currentPeriod.Id },
        new PeriodTrialBalance { AccountCode = 513, SubAccountCode = 52, Amount = 200, PeriodId = currentPeriod.Id },
        new PeriodTrialBalance { AccountCode = 514, SubAccountCode = 53, Amount = 300, PeriodId = currentPeriod.Id },
        new PeriodTrialBalance { AccountCode = 522, SubAccountCode = 54, Amount = 400, PeriodId = currentPeriod.Id },
        new PeriodTrialBalance { AccountCode = 523, SubAccountCode = 55, Amount = 500, PeriodId = currentPeriod.Id },
        new PeriodTrialBalance { AccountCode = 524, SubAccountCode = 56, Amount = 600, PeriodId = currentPeriod.Id },
        new PeriodTrialBalance { AccountCode = 525, SubAccountCode = 57, Amount = 700, PeriodId = currentPeriod.Id },
        new PeriodTrialBalance { AccountCode = 526, SubAccountCode = 58, Amount = 800, PeriodId = currentPeriod.Id }
    };

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());

        var rouToken = result.FirstOrDefault(x => x.AssignedToken == TokenType.TangibleFixedAssetsROU.ToString());

        Assert.NotNull(rouToken);
        Assert.Equal(data.ClientId, rouToken!.ClientId);
        Assert.Equal(currentPeriod!.Id, rouToken.AccountPeriodId);
        Assert.Equal("TangibleFixedAssetsROU", rouToken.AssignedToken);
        Assert.Equal("512", rouToken.Token1);
        Assert.Equal("513", rouToken.Token2);
        Assert.Equal("514", rouToken.Token3);
        Assert.Equal("522", rouToken.Token4);
        Assert.Equal("523", rouToken.Token5);
        Assert.Equal("524", rouToken.Token6);
        Assert.Equal("525", rouToken.Token7);
        Assert.Equal("526", rouToken.Token8);
        Assert.Equal("Total", rouToken.Token9);
        Assert.Equal(8, rouToken.TokenCount);
    }

    private static AccountsBuilderReportingMessageDto GetAccountsBuilderReportingMessageDto()
    {
        return new AccountsBuilderReportingMessageDto
        {
            ClientId = Guid.NewGuid(),
            ClientData = new ClientDto { CompanyType = "Limited" },
            ReportType = "FRS102",
            ReportingPeriods = Data.GetReportingPeriods(),
            TrialBalanceData = Data.GetPeriodTrialBalanceData()
        };
    }

    [Fact]
    public async Task GetProcessedMultiColumnToken_Should_Return_With_SocieLimitedCompany_IFRS()
    {
        var data = GetAccountsBuilderReportingMessageDto();
        data.ReportType = "IFRS";
        data.ClientData!.CompanyType = "Limited";
        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);
        var previousPeriod = data.ReportingPeriods.MinBy(x => x.EndDate);

        data.TrialBalanceData = new List<AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance>
        {
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 966,
                Amount = 100,
                PeriodId = currentPeriod!.Id
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 969,
                Amount = 200,
                PeriodId = currentPeriod!.Id
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 970,
                Amount = 300,
                PeriodId = previousPeriod!.Id
            },
            new AccountsProduction.AccountsBuilder.Domain.PeriodTrialBalance
            {
                AccountCode = 971,
                Amount = 400,
                PeriodId = previousPeriod!.Id
            }
        };

        var result = await MultiTokenHelper.GetProcessedMultiColumnToken(data, new CancellationToken());
        Assert.NotNull(result);

        var socieLimitedCompany = result.First(x => x.AssignedToken == TokenType.SocieLimitedCompany.ToString());

        Assert.Equal(data.ClientId, socieLimitedCompany.ClientId);
        Assert.Equal(currentPeriod!.Id, socieLimitedCompany.AccountPeriodId);
        Assert.Equal("SocieLimitedCompany", socieLimitedCompany.AssignedToken);
        Assert.Null(socieLimitedCompany.Token2);
        Assert.Null(socieLimitedCompany.Token3);
        Assert.Null(socieLimitedCompany.Token4);
        Assert.Equal("966", socieLimitedCompany.Token5);
        Assert.Equal("969", socieLimitedCompany.Token6);
        Assert.Equal("970", socieLimitedCompany.Token7);
        Assert.Equal("971", socieLimitedCompany.Token8);
        Assert.Equal("Total", socieLimitedCompany.Token9);
        Assert.Equal(4, socieLimitedCompany.TokenCount);
    }
}
