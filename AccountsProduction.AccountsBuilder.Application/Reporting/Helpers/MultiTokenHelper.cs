using AccountsProduction.AccountsBuilder.Application.Common;
using AccountsProduction.AccountsBuilder.Application.Common.Dto.FinancialData;
using AccountsProduction.AccountsBuilder.Application.Reporting.Dto;
using AccountsProduction.AccountsBuilder.Domain;
using AccountsProduction.AccountsBuilder.Domain.Reporting.Other;
using System.Linq;

namespace AccountsProduction.AccountsBuilder.Application.Reporting.Helpers;

public static class MultiTokenHelper
{
    public static List<int> GetNumbersForTokenType(TokenType tokenType, string? businessType)
    {
        switch (tokenType)
        {
            case TokenType.SocieLimitedCompany:
                return new List<int> { 966, 969, 968, 970, 971, 972, 973, 974 };
            case TokenType.TangibleFixedAssets:
                return new List<int> { 512, 513, 514, 522, 523, 524, 525, 526 };
            case TokenType.FixedAssetInvestmentsPart1:
                return GetFixedAssetInvestmentsPart1Numbers(businessType);
            case TokenType.FixedAssetInvestmentsPart2:
                return GetFixedAssetInvestmentsPart2Numbers(businessType);
            case TokenType.RomiLlp:
                return new List<int> { 975, 976, 977, 978, 970, 972, 973, 974, 999, 985, 987, 980, 981, 997, 998, 989, 990 };
            case TokenType.Investments:
                return new List<int> { 542, 548, 549, 550, 556, 557, 561 };
            case TokenType.LoansOthFinAssets:
                return new List<int> { 543, 551, 552, 553, 559 };
            case TokenType.BorrowingsMaturityCY:
                return GetBorrowingsMaturityCodes(businessType);
            case TokenType.TangibleFixedAssetsROU:
                return new List<int> { 512, 513, 514, 522, 523, 524, 525, 526 };
            case TokenType.BorrowingsMaturityPY:
                return GetBorrowingsMaturityCodes(businessType);
            default:
                throw new ArgumentException($"Unsupported TokenType: {tokenType}");
        }
    }

    public static List<int> GetSubAccountCodes(string reportType, string bussinessType, TokenType tokenType, int AccountCode)
    {
        var borrowingsMaturityMapping = new Dictionary<string, Dictionary<string, Dictionary<int, List<int>>>>
    {
        {
            ReportStandardType.IFRS,
            new Dictionary<string, Dictionary<int, List<int>>>
            {
                {
                    BusinessTypes.LimitedBusinessType,
                    new Dictionary<int, List<int>>
                    {
                        { 910, new List<int> {1,2,3,4} },
                        { 913, new List<int> {1,2,3,4} },
                        { 916, new List<int> {1,2,3,4} },
                        { 919, new List<int> {1,2,3,4} },
                        { 922, new List<int> {1,2,3,4} },
                        { 925, new List<int> {1,2,3,4} }
                    }
                }
            }
        }
    };

    var tokenTypeMap = new Dictionary<TokenType, Dictionary<string, Dictionary<string, Dictionary<int, List<int>>>>>
    {
        { TokenType.BorrowingsMaturityCY, borrowingsMaturityMapping },
        { TokenType.BorrowingsMaturityPY, borrowingsMaturityMapping }
    };

        if (tokenTypeMap.TryGetValue(tokenType, out var tokenTypeDict) &&
            tokenTypeDict.TryGetValue(reportType, out var reportTypeDict) &&
            reportTypeDict.TryGetValue(bussinessType, out var bussinessTypeDict) &&
            bussinessTypeDict.TryGetValue(AccountCode, out var subAccountCodes))
        {
            return subAccountCodes;
        }
        return new List<int>();
    }

    private static List<int> GetFixedAssetInvestmentsPart1Numbers(string? businessType)
    {
        Dictionary<string, List<int>> businessTypeNumbers = new Dictionary<string, List<int>>
        {
            { BusinessTypes.LimitedBusinessType, new List<int> { 542, 548, 549, 550, 556, 557, 561 }},
            { BusinessTypes.LlpBusinessType, new List<int> { 542, 548, 549, 550, 556, 557 }},
            { BusinessTypes.IncorporatedCharityBusinessType, new List<int> { 542, 556, 557, 561, 564, 566, 567 }},
            { BusinessTypes.UnincorporatedCharityBusinessType, new List<int> { 542, 556, 557, 561, 564, 566, 567 }}
        };

        if (businessTypeNumbers.ContainsKey(businessType))
            return businessTypeNumbers[businessType];

        throw new ArgumentException($"Unsupported business type: {businessType}");
    }

    private static List<int> GetFixedAssetInvestmentsPart2Numbers(string? businessType)
    {
        Dictionary<string, List<int>> businessTypeNumbers = new Dictionary<string, List<int>>
        {
            { BusinessTypes.LimitedBusinessType, new List<int> { 543, 551, 552, 553, 559 }},
            { BusinessTypes.LlpBusinessType, new List<int> { 543, 551, 552, 553, 559 }},
            { BusinessTypes.IncorporatedCharityBusinessType, new List<int> { 543, 559, 565 }},
            { BusinessTypes.UnincorporatedCharityBusinessType, new List<int> { 543, 559, 565 }}
        };

        if (businessTypeNumbers.ContainsKey(businessType))
            return businessTypeNumbers[businessType];

        throw new ArgumentException($"Unsupported business type: {businessType}");
    }

    private static List<int> GetBorrowingsMaturityCodes(string? businessType)
    {
        Dictionary<string, List<int>> businessTypeNumbers = new Dictionary<string, List<int>>
        {
            { BusinessTypes.LimitedBusinessType, new List<int> {
                // MAT1
                839, 840, 849, 850, 851, 852, 869, 870, 871, 872, 873, 874,
                909, 912, 915, 918, 921, 924,

                // MAT2
                841, 842, 853, 854, 855, 856, 875, 876, 877, 878, 879, 880,
                910, 913, 916, 919, 922, 925,

                // MAT3
                843, 844, 857, 858, 859, 860, 881, 882, 883, 884, 885, 886,
                910, 913, 916, 919, 922, 925,

                // MAT4
                845, 846, 847, 848, 861, 862, 863, 864, 865, 866, 867, 868,
                887, 888, 889, 890, 891, 892, 893, 894, 895, 896, 897, 898,
                911, 914, 917, 920, 923, 926 }}
        };

        if (businessTypeNumbers.ContainsKey(businessType))
            return businessTypeNumbers[businessType];

        throw new ArgumentException($"Unsupported business type: {businessType}");
    }

    public static Dictionary<int, List<List<int>>> GetTokenPatternForTokenType(TokenType tokenType, string? businessType)
    {
        switch (tokenType)
        {
            case TokenType.SocieLimitedCompany:
                return new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 966, 969, 968, 970, 971, 972, 973, 974 }}},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 966, 969, 968, 970, 971, 972, 973 }, new List<int> { 969, 968, 970, 971, 972, 973, 974 }, new List<int> { }}},
                    {3,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 966, 969, 968, 970, 971, 972 }, new List<int> { 969, 968, 970, 971, 972, 973 }, new List<int> { 968, 970, 971, 972, 973, 974 }, new List<int> { }}},
                    {4,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 966, 969, 968, 970, 971 }, new List<int> { 969, 968, 970, 971, 972 }, new List<int> { 968, 970, 971, 972, 973 }, new List<int> { 970, 971, 972, 973, 974 }, new List<int> { }}},
                    {5,  new List<List<int>>{ new List<int> { }, new List<int> { 966, 969, 968, 970 }, new List<int> { 969, 968, 970, 971 }, new List<int> { 968, 970, 971, 972 }, new List<int> { }, new List<int> { }, new List<int> { 970, 971, 972, 973 }, new List<int> { 971, 972, 973, 974 }, new List<int> { }}},
                    {6,  new List<List<int>>{ new List<int> { 966, 969, 968 }, new List<int> { 969, 968, 970 }, new List<int> { 968, 970, 971 }, new List<int> { 970, 971, 972 }, new List<int> { }, new List<int> { }, new List<int> { 971, 972, 973 }, new List<int> { 972, 973, 974 }, new List<int> { }}},
                    {7,  new List<List<int>>{ new List<int> { 966, 969 }, new List<int> { 969, 968 }, new List<int> { 968, 970 }, new List<int> { 970, 971 }, new List<int> { }, new List<int> { 971, 972 }, new List<int> { 972, 973 }, new List<int> { 973, 974 }, new List<int> { }}},
                    {8,  new List<List<int>>{ new List<int> { 966 }, new List<int> { 969 }, new List<int> { 968 }, new List<int> { 970 }, new List<int> { 971 }, new List<int> { 972 }, new List<int> { 973 }, new List<int> { 974 }, new List<int> { }}}
                };
            case TokenType.TangibleFixedAssets:
                return new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 512, 513, 514, 522, 523, 524, 525, 526 }}},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 512, 513, 514, 522, 523, 524, 525 }, new List<int> { 513, 514, 522, 523, 524, 525, 526 }, new List<int> { }}},
                    {3,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 512, 513, 514, 522, 523, 524 }, new List<int> { 513, 514, 522, 523, 524, 525 }, new List<int> { 514, 522, 523, 524, 525, 526 }, new List<int> { }}},
                    {4,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 512, 513, 514, 522, 523 }, new List<int> { 513, 514, 522, 523, 524 }, new List<int> { 514, 522, 523, 524, 525 }, new List<int> { 522, 523, 524, 525, 526 }, new List<int> { }}},
                    {5,  new List<List<int>>{ new List<int> { }, new List<int> { 512, 513, 514, 522 }, new List<int> { 513, 514, 522, 523 }, new List<int> { 514, 522, 523, 524 }, new List<int> { }, new List<int> { }, new List<int> { 522, 523, 524, 525 }, new List<int> { 523, 524, 525, 526 }, new List<int> { }}},
                    {6,  new List<List<int>>{ new List<int> { 512, 513, 514 }, new List<int> { 513, 514, 522 }, new List<int> { 514, 522, 523 }, new List<int> { 522, 523, 524 }, new List<int> { }, new List<int> { }, new List<int> { 523, 524, 525 }, new List<int> { 524, 525, 526 }, new List<int> { }}},
                    {7,  new List<List<int>>{ new List<int> { 512, 513 }, new List<int> { 513, 514 }, new List<int> { 514, 522 }, new List<int> { 522, 523 }, new List<int> { }, new List<int> { 523, 524 }, new List<int> { 524, 525 }, new List<int> { 525, 526 }, new List<int> { }}},
                    {8,  new List<List<int>>{ new List<int> { 512 }, new List<int> { 513 }, new List<int> { 514 }, new List<int> { 522 }, new List<int> { 523 }, new List<int> { 524 }, new List<int> { 525 }, new List<int> { 526 }, new List<int> { }}}
                };
            case TokenType.FixedAssetInvestmentsPart1:
                return GetFixedAssetInvestmentsPart1TokenPattern(businessType);
            case TokenType.FixedAssetInvestmentsPart2:
                return GetFixedAssetInvestmentsPart2TokenPattern(businessType);
            case TokenType.Investments:
                return new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549, 550, 556, 557, 561 } }},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549, 550, 556, 557 }, new List<int> { 548, 549, 550, 556, 557, 561 }, new List<int> { }}},
                    {3,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549, 550, 556 }, new List<int> { 548, 549, 550, 556, 557 }, new List<int> { 549, 550, 556, 557, 561 }, new List<int> { }}},
                    {4,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549, 550 }, new List<int> { 548, 549, 550, 556 }, new List<int> { }, new List<int> { 549, 550, 556, 557 }, new List<int> { 550, 556, 557, 561 }, new List<int> { }}},
                    {5,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549 }, new List<int> { 548, 549, 550 }, new List<int> { 549, 550, 556 }, new List<int> { 550, 556, 557 }, new List<int> { 556, 557, 561 }, new List<int> { }}},
                    {6,  new List<List<int>>{ new List<int> { }, new List<int> { 542, 548 }, new List<int> { 548, 549 }, new List<int> { 549, 550 }, new List<int> { 550, 556 }, new List<int> { 556, 557 }, new List<int> { 557, 561 }, new List<int> { }}},
                    {7,  new List<List<int>>{ new List<int> { 542 }, new List<int> { 548 }, new List<int> { 549 }, new List<int> { 550 }, new List<int> { 556 }, new List<int> { 557 }, new List<int> { 561 }, new List<int> { }}}
                };
            case TokenType.LoansOthFinAssets:
                return new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 543, 551, 552, 553, 559 } }},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 543, 551, 552, 553 }, new List<int> { 551, 552, 553, 559 }, new List<int> { } }},
                    {3,  new List<List<int>>{ new List<int> { }, new List<int> { 543, 551, 552 }, new List<int> { 551, 552, 553 }, new List<int> { }, new List<int> { 552, 553, 559 }, new List<int> { } }},
                    {4,  new List<List<int>>{ new List<int> { }, new List<int> { 543, 551 }, new List<int> { 551, 552 }, new List<int> { 552, 553 }, new List<int> { 553, 559 }, new List<int> { } }},
                    {5,  new List<List<int>>{ new List<int> { 543 }, new List<int> { 551 }, new List<int> { 552 }, new List<int> { 553 }, new List<int> { 559 }, new List<int> { } }}
                };
            case TokenType.TangibleFixedAssetsROU:
                return new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 512, 513, 514, 522, 523, 524, 525, 526 }}},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 512, 513, 514, 522, 523, 524, 525 }, new List<int> { 513, 514, 522, 523, 524, 525, 526 }, new List<int> { }}},
                    {3,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 512, 513, 514, 522, 523, 524 }, new List<int> { 513, 514, 522, 523, 524, 525 }, new List<int> { 514, 522, 523, 524, 525, 526 }, new List<int> { }}},
                    {4,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 512, 513, 514, 522, 523 }, new List<int> { 513, 514, 522, 523, 524 }, new List<int> { 514, 522, 523, 524, 525 }, new List<int> { 522, 523, 524, 525, 526 }, new List<int> { }}},
                    {5,  new List<List<int>>{ new List<int> { }, new List<int> { 512, 513, 514, 522 }, new List<int> { 513, 514, 522, 523 }, new List<int> { 514, 522, 523, 524 }, new List<int> { }, new List<int> { }, new List<int> { 522, 523, 524, 525 }, new List<int> { 523, 524, 525, 526 }, new List<int> { }}},
                    {6,  new List<List<int>>{ new List<int> { 512, 513, 514 }, new List<int> { 513, 514, 522 }, new List<int> { 514, 522, 523 }, new List<int> { 522, 523, 524 }, new List<int> { }, new List<int> { }, new List<int> { 523, 524, 525 }, new List<int> { 524, 525, 526 }, new List<int> { }}},
                    {7,  new List<List<int>>{ new List<int> { 512, 513 }, new List<int> { 513, 514 }, new List<int> { 514, 522 }, new List<int> { 522, 523 }, new List<int> { }, new List<int> { 523, 524 }, new List<int> { 524, 525 }, new List<int> { 525, 526 }, new List<int> { }}},
                    {8,  new List<List<int>>{ new List<int> { 512 }, new List<int> { 513 }, new List<int> { 514 }, new List<int> { 522 }, new List<int> { 523 }, new List<int> { 524 }, new List<int> { 525 }, new List<int> { 526 }, new List<int> { }}}
                };
            default:
                throw new ArgumentException($"Unsupported TokenType: {tokenType}");
        }
    }

    private static Dictionary<int, List<List<int>>> GetFixedAssetInvestmentsPart1TokenPattern(string? businessType)
    {
        Dictionary<string, Dictionary<int, List<List<int>>>> tokenPatterns = new Dictionary<string, Dictionary<int, List<List<int>>>>
        {
            { 
                BusinessTypes.LimitedBusinessType, new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549, 550, 556, 557, 561 } }},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549, 550, 556, 557 }, new List<int> { 548, 549, 550, 556, 557, 561 }, new List<int> { }}},
                    {3,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549, 550, 556 }, new List<int> { 548, 549, 550, 556, 557 }, new List<int> { 549, 550, 556, 557, 561 }, new List<int> { }}},
                    {4,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549, 550 }, new List<int> { 548, 549, 550, 556 }, new List<int> { }, new List<int> { 549, 550, 556, 557 }, new List<int> { 550, 556, 557, 561 }, new List<int> { }}},
                    {5,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549 }, new List<int> { 548, 549, 550 }, new List<int> { 549, 550, 556 }, new List<int> { 550, 556, 557 }, new List<int> { 556, 557, 561 }, new List<int> { }}},
                    {6,  new List<List<int>>{ new List<int> { }, new List<int> { 542, 548 }, new List<int> { 548, 549 }, new List<int> { 549, 550 }, new List<int> { 550, 556 }, new List<int> { 556, 557 }, new List<int> { 557, 561 }, new List<int> { }}},
                    {7,  new List<List<int>>{ new List<int> { 542 }, new List<int> { 548 }, new List<int> { 549 }, new List<int> { 550 }, new List<int> { 556 }, new List<int> { 557 }, new List<int> { 561 }, new List<int> { }}}
                }
            },
            { 
                BusinessTypes.LlpBusinessType, new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549, 550, 556, 557 } }},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549, 550, 556 }, new List<int> { 548, 549, 550, 556, 557 }, new List<int> { }}},
                    {3,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549, 550 }, new List<int> { 548, 549, 550, 556 }, new List<int> { 549, 550, 556, 557 }, new List<int> { }}},
                    {4,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { 542, 548, 549 }, new List<int> { 548, 549, 550 }, new List<int> { }, new List<int> { 549, 550, 556 }, new List<int> { 550, 556, 557 }, new List<int> { }}},
                    {5,  new List<List<int>>{ new List<int> { }, new List<int> { 542, 548 }, new List<int> { 548, 549 }, new List<int> { 549, 550 }, new List<int> { }, new List<int> { 550, 556 }, new List<int> { 556, 557 }, new List<int> { }}},
                    {6,  new List<List<int>>{ new List<int> { }, new List<int> { 542 }, new List<int> { 548 }, new List<int> { 549 }, new List<int> { 550 }, new List<int> { 556 }, new List<int> { 557 }, new List<int> { }}}
                }
            },
            {
                BusinessTypes.IncorporatedCharityBusinessType, new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 556, 557, 561, 564, 566, 567 } }},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 556, 557, 561, 564, 566 }, new List<int> { 556, 557, 561, 564, 566, 567 }, new List<int> { }}},
                    {3,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 556, 557, 561, 564 }, new List<int> { 556, 557, 561, 564, 566 }, new List<int> { 557, 561, 564, 566, 567 }, new List<int> { }}},
                    {4,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { 542, 556, 557, 561 }, new List<int> { 556, 557, 561, 564 }, new List<int> { }, new List<int> { 557, 561, 564, 566 }, new List<int> { 561, 564, 566, 567 }, new List<int> { }}},
                    {5,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { 542, 556, 557 }, new List<int> { 556, 557, 561 }, new List<int> { 557, 561, 564 }, new List<int> { 561, 564, 566 }, new List<int> { 564, 566, 567 }, new List<int> { }}},
                    {6,  new List<List<int>>{ new List<int> { }, new List<int> { 542, 556 }, new List<int> { 556, 557 }, new List<int> { 557, 561 }, new List<int> { 561, 564 }, new List<int> { 564, 566 }, new List<int> { 566, 567 }, new List<int> { }}},
                    {7,  new List<List<int>>{ new List<int> { 542 }, new List<int> { 556 }, new List<int> { 557 }, new List<int> { 561 }, new List<int> { 564 }, new List<int> { 566 }, new List<int> { 567 }, new List<int> { }}}
                }
            },
            {
                BusinessTypes.UnincorporatedCharityBusinessType, new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 556, 557, 561, 564, 566, 567 } }},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 556, 557, 561, 564, 566 }, new List<int> { 556, 557, 561, 564, 566, 567 }, new List<int> { }}},
                    {3,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 542, 556, 557, 561, 564 }, new List<int> { 556, 557, 561, 564, 566 }, new List<int> { 557, 561, 564, 566, 567 }, new List<int> { }}},
                    {4,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { 542, 556, 557, 561 }, new List<int> { 556, 557, 561, 564 }, new List<int> { }, new List<int> { 557, 561, 564, 566 }, new List<int> { 561, 564, 566, 567 }, new List<int> { }}},
                    {5,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { 542, 556, 557 }, new List<int> { 556, 557, 561 }, new List<int> { 557, 561, 564 }, new List<int> { 561, 564, 566 }, new List<int> { 564, 566, 567 }, new List<int> { }}},
                    {6,  new List<List<int>>{ new List<int> { }, new List<int> { 542, 556 }, new List<int> { 556, 557 }, new List<int> { 557, 561 }, new List<int> { 561, 564 }, new List<int> { 564, 566 }, new List<int> { 566, 567 }, new List<int> { }}},
                    {7,  new List<List<int>>{ new List<int> { 542 }, new List<int> { 556 }, new List<int> { 557 }, new List<int> { 561 }, new List<int> { 564 }, new List<int> { 566 }, new List<int> { 567 }, new List<int> { }}}
                }
            }
        };

        if (tokenPatterns.ContainsKey(businessType))
            return tokenPatterns[businessType];

        throw new ArgumentException($"Unsupported business type: {businessType}");
    }

    private static Dictionary<int, List<List<int>>> GetFixedAssetInvestmentsPart2TokenPattern(string? businessType)
    {
        Dictionary<string, Dictionary<int, List<List<int>>>> tokenPatterns = new Dictionary<string, Dictionary<int, List<List<int>>>>
        {
            {
                BusinessTypes.LimitedBusinessType, new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 543, 551, 552, 553, 559 } }},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 543, 551, 552, 553 }, new List<int> { 551, 552, 553, 559 }, new List<int> { } }},
                    {3,  new List<List<int>>{ new List<int> { }, new List<int> { 543, 551, 552 }, new List<int> { 551, 552, 553 }, new List<int> { }, new List<int> { 552, 553, 559 }, new List<int> { } }},
                    {4,  new List<List<int>>{ new List<int> { }, new List<int> { 543, 551 }, new List<int> { 551, 552 }, new List<int> { 552, 553 }, new List<int> { 553, 559 }, new List<int> { } }},
                    {5,  new List<List<int>>{ new List<int> { 543 }, new List<int> { 551 }, new List<int> { 552 }, new List<int> { 553 }, new List<int> { 559 }, new List<int> { } }}
                }
            },
            {
                BusinessTypes.LlpBusinessType, new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 543, 551, 552, 553, 559 } }},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 543, 551, 552, 553 }, new List<int> { 551, 552, 553, 559 }, new List<int> { } }},
                    {3,  new List<List<int>>{ new List<int> { }, new List<int> { 543, 551, 552 }, new List<int> { 551, 552, 553 }, new List<int> { }, new List<int> { 552, 553, 559 }, new List<int> { } }},
                    {4,  new List<List<int>>{ new List<int> { }, new List<int> { 543, 551 }, new List<int> { 551, 552 }, new List<int> { 552, 553 }, new List<int> { 553, 559 }, new List<int> { } }},
                    {5,  new List<List<int>>{ new List<int> { 543 }, new List<int> { 551 }, new List<int> { 552 }, new List<int> { 553 }, new List<int> { 559 }, new List<int> { } }}
                }
            },
            {
                BusinessTypes.IncorporatedCharityBusinessType, new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 543, 559, 565 } }},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { 543, 559 }, new List<int> { 559, 565 }, new List<int> { } }},
                    {3,  new List<List<int>>{ new List<int> { 543 }, new List<int> { 559 }, new List<int> { 565 }, new List<int> { } }}
                }
            },
            {
                BusinessTypes.UnincorporatedCharityBusinessType, new Dictionary<int, List<List<int>>>
                {
                    {1,  new List<List<int>>{ new List<int> { }, new List<int> { }, new List<int> { }, new List<int> { 543, 559, 565 } }},
                    {2,  new List<List<int>>{ new List<int> { }, new List<int> { 543, 559 }, new List<int> { 559, 565 }, new List<int> { } }},
                    {3,  new List<List<int>>{ new List<int> { 543 }, new List<int> { 559 }, new List<int> { 565 }, new List<int> { } }}
                }
            }
        };

        if (tokenPatterns.ContainsKey(businessType))
            return tokenPatterns[businessType];

        throw new ArgumentException($"Unsupported business type: {businessType}");
    }

    public static bool IsMultiTokenEligible(string reportType, string businessType, TokenType tokenType)
    {
        var tokenTypeMap = new Dictionary<TokenType, Dictionary<string, HashSet<string>>>
        {
            {
                TokenType.SocieLimitedCompany,
                new Dictionary<string, HashSet<string>>
                {
                    { ReportStandardType.FRS102, new HashSet<string> { BusinessTypes.LimitedBusinessType } },
                    { ReportStandardType.IFRS, new HashSet<string> { BusinessTypes.LimitedBusinessType } }
                }
            },
            {
                TokenType.TangibleFixedAssets,
                new Dictionary<string, HashSet<string>>
                {
                    { ReportStandardType.FRS102, new HashSet<string> { BusinessTypes.LimitedBusinessType, BusinessTypes.LlpBusinessType } },
                    { ReportStandardType.CHARITY_SORP_FRS102, new HashSet<string> { BusinessTypes.IncorporatedCharityBusinessType, BusinessTypes.UnincorporatedCharityBusinessType } },
                    { ReportStandardType.IFRS, new HashSet<string> { BusinessTypes.LimitedBusinessType} }
                }
            },
            {
                TokenType.FixedAssetInvestmentsPart1,
                new Dictionary<string, HashSet<string>>
                {
                    { ReportStandardType.FRS102, new HashSet<string> { BusinessTypes.LimitedBusinessType, BusinessTypes.LlpBusinessType } },
                    { ReportStandardType.CHARITY_SORP_FRS102, new HashSet<string> { BusinessTypes.IncorporatedCharityBusinessType, BusinessTypes.UnincorporatedCharityBusinessType } }
                }
            },
            {
                TokenType.FixedAssetInvestmentsPart2,
                new Dictionary<string, HashSet<string>>
                {
                    { ReportStandardType.FRS102, new HashSet<string> { BusinessTypes.LimitedBusinessType, BusinessTypes.LlpBusinessType } },
                    { ReportStandardType.CHARITY_SORP_FRS102, new HashSet<string> { BusinessTypes.IncorporatedCharityBusinessType, BusinessTypes.UnincorporatedCharityBusinessType } }
                }
            },
            {
                TokenType.RomiLlp,
                new Dictionary<string, HashSet<string>>
                {
                    { ReportStandardType.FRS102, new HashSet<string> { BusinessTypes.LlpBusinessType } }
                }
            },
            {
                TokenType.Investments,
                new Dictionary<string, HashSet<string>>
                {
                    { ReportStandardType.IFRS, new HashSet<string> { BusinessTypes.LimitedBusinessType } }
                }
            },
            {
                TokenType.LoansOthFinAssets,
                new Dictionary<string, HashSet<string>>
                {
                    { ReportStandardType.IFRS, new HashSet<string> { BusinessTypes.LimitedBusinessType } }
                }
            },
            {
                TokenType.BorrowingsMaturityCY,
                new Dictionary<string, HashSet<string>>
                {
                    { ReportStandardType.IFRS, new HashSet<string> { BusinessTypes.LimitedBusinessType } }
                }
            },
            {
                TokenType.TangibleFixedAssetsROU,
                new Dictionary<string, HashSet<string>>
                {
                    { ReportStandardType.IFRS, new HashSet<string> { BusinessTypes.LimitedBusinessType } }
                }
            },
            {
                TokenType.BorrowingsMaturityPY,
                new Dictionary<string, HashSet<string>>
                {
                    { ReportStandardType.IFRS, new HashSet<string> { BusinessTypes.LimitedBusinessType } }
                }
            },
        };

        var isEligible = tokenTypeMap.ContainsKey(tokenType) &&
                         tokenTypeMap[tokenType].ContainsKey(reportType) &&
                         tokenTypeMap[tokenType][reportType].Contains(businessType);

        return isEligible;
    }

    public static async Task<List<MultiColumnToken>> GetProcessedMultiColumnToken(AccountsBuilderReportingMessageDto data, CancellationToken cancellationToken)
    {
        var nonTrialBalanceData = new NonTrialBalanceDataDto
        {
            IsAllocatedProfitAvailable = await CheckAllocatedProfitAvailable(data.ProfitShareData),
            IsProfitLossReserveAvailable = await CheckProfitLossReserve(data.FinancialData)
        };

        var tasks = new List<Task<MultiColumnToken?>>
        {
            ProcessMultiColumnToken(TokenType.SocieLimitedCompany, data, nonTrialBalanceData),
            ProcessMultiColumnToken(TokenType.TangibleFixedAssets, data),
            ProcessMultiColumnToken(TokenType.FixedAssetInvestmentsPart1, data),
            ProcessMultiColumnToken(TokenType.FixedAssetInvestmentsPart2, data),
            ProcessMultiColumnToken(TokenType.RomiLlp, data, nonTrialBalanceData),
            ProcessMultiColumnToken(TokenType.Investments, data),
            ProcessMultiColumnToken(TokenType.LoansOthFinAssets, data),
            ProcessMultiColumnToken(TokenType.BorrowingsMaturityCY, data),
            ProcessMultiColumnToken(TokenType.TangibleFixedAssetsROU, data),
            ProcessMultiColumnToken(TokenType.BorrowingsMaturityPY, data)
        };

        var results = await Task.WhenAll(tasks);
        var multitokens = results.Where(r => r != null).Select(r => r!).ToList();

        return multitokens;
    }

    public static async Task<MultiColumnToken?> ProcessMultiColumnToken(
        TokenType tokenType,
        AccountsBuilderReportingMessageDto data,
        NonTrialBalanceDataDto? nonTrialBalanceData = null)
    {
        var businessType = data.ClientData?.CompanyType!;

        if (!IsMultiTokenEligible(data.ReportType, businessType, tokenType))
            return null;

        var (currentPeriodId, tokenTypeTrialBalances) = await GetTrialBalancesAsync(tokenType, data);

        var accountCodes = await GetAccountCodes(tokenType, tokenTypeTrialBalances, nonTrialBalanceData);

        if (accountCodes.Count == 0)
            return null;

        (int tokenCount, List<string?> orderedToken) result;
        switch (tokenType)
        {
            case TokenType.BorrowingsMaturityCY:
            case TokenType.BorrowingsMaturityPY:
                var subAccountCode = tokenTypeTrialBalances
                    .Select(tc => (tc.AccountCode, tc.SubAccountCode))
                    .ToList();
                result = MultiTokenHelper.GetOrderedTokenBorrowingsMaturity(subAccountCode);
                return await AddMultiColumnToken(data.ClientId, currentPeriodId, result.tokenCount, tokenType, result.orderedToken);

            case TokenType.RomiLlp:
                result = MultiTokenHelper.GetOrderedTokenRomiLlp(tokenType, accountCodes);
                break;

            default:
                result = MultiTokenHelper.GetOrderedToken(businessType, tokenType, accountCodes);
                break;
        }

        return await AddMultiColumnToken(data.ClientId, currentPeriodId, result.tokenCount, tokenType, result.orderedToken);
    }

    public static (int, List<string?>) GetOrderedToken(string? businessType, TokenType tokenType, List<int> accountCodes)
    {
        var tokenCount = accountCodes.Count;
        var tokenPatterns = GetTokenPatternForTokenType(tokenType, businessType)[tokenCount];

        List<string?> orderedToken = new();

        foreach (var pattern in tokenPatterns)
        {
            string? accountCode = null;
            foreach (var code in pattern)
            {
                var isCodeMatch = false;
                for (int i = accountCodes.Count - 1; i >= 0; i--)
                {
                    if (accountCodes[i] == code)
                    {
                        accountCode = code.ToString();
                        accountCodes.RemoveAt(i);
                        isCodeMatch = true;
                        break;
                    }
                }

                if (isCodeMatch)
                    break;
            }
            orderedToken.Add(accountCode);
        }

        return (tokenCount, orderedToken);
    }

    public static (int, List<string?>) GetOrderedTokenBorrowingsMaturity(
    List<(int AccountCode, int? SubAccount)> subAccountCodes)
    {
        const int totalTokenSlots = 5;

        var matParentCodes = new HashSet<int> { 910, 913, 916, 919, 922, 925 };
        var mat1Codes = new HashSet<int> { 839, 840, 849, 850, 851, 852, 869, 870, 871, 872, 873, 874, 909, 912, 915, 918, 921, 924 };
        var mat2Codes = new HashSet<int> { 841, 842, 853, 854, 855, 856, 875, 876, 877, 878, 879, 880, 910, 913, 916, 919, 922, 925 };
        var mat3Codes = new HashSet<int> { 843, 844, 857, 858, 859, 860, 881, 882, 883, 884, 885, 886, 910, 913, 916, 919, 922, 925 };
        var mat4Codes = new HashSet<int> { 845, 846, 847, 848, 861, 862, 863, 864, 865, 866, 867, 868, 887, 888, 889, 890, 891, 892, 893, 894, 895, 896, 897, 898, 911, 914, 917, 920, 923, 926 };

        bool hasMAT1 = false, hasMAT2 = false, hasMAT3 = false, hasMAT4 = false;

        foreach (var (accountCode, subAccount) in subAccountCodes)
        {
            if (matParentCodes.Contains(accountCode) && subAccount.HasValue)
            {
                if (subAccount == 1)
                {
                    hasMAT2 = true;
                    continue;
                }
                if (subAccount is >= 2 and <= 4)
                {
                    hasMAT3 = true;
                    continue;
                }
            }
            hasMAT1 |= mat1Codes.Contains(accountCode);
            hasMAT2 |= mat2Codes.Contains(accountCode);
            hasMAT3 |= mat3Codes.Contains(accountCode);
            hasMAT4 |= mat4Codes.Contains(accountCode);
        }

        var presentTokens = GetMATTokens(hasMAT1, hasMAT2, hasMAT3, hasMAT4);
        int tokenCount = presentTokens.Count;
        var orderedTokens = new string?[totalTokenSlots];

        if (tokenCount == 1)
        {
            orderedTokens[4] = presentTokens[0];
        }
        else
        {
            int tokenIndex = totalTokenSlots - tokenCount - 1;
            for (int i = 0; i < tokenCount; i++)
            {
                orderedTokens[tokenIndex] = presentTokens[i];
                tokenIndex++;
            }
            orderedTokens[4] = "Total";
        }

        return (tokenCount, orderedTokens.ToList());
    }

    private static List<string> GetMATTokens(bool hasMAT1, bool hasMAT2, bool hasMAT3, bool hasMAT4)
    {
        var tokens = new List<string>();
        if (hasMAT1) tokens.Add("MAT1");
        if (hasMAT2) tokens.Add("MAT2");
        if (hasMAT3) tokens.Add("MAT3");
        if (hasMAT4) tokens.Add("MAT4");
        return tokens;
    }

    public static (int, List<string?>) GetOrderedTokenRomiLlp(TokenType tokenType, List<int> accountCodes)
    {
        int totalDebtToken = 4;
        int totalEquityToken = 5;

        var debts = new List<List<int>>() {
            new List<int>() { 985, 987, 980, 981, 997, 998 },
            new List<int>() { 989, 990 }
        };

        var equities = new List<List<int>>() {
            new List<int>() { 975, 976, 977, 978 },
            new List<int>() { 970 },
            new List<int>() { 972, 973, 974, 999 }
        };

        List<string> debtTokens = GenerateRomiTokens(accountCodes, debts, "DB");
        List<string> equityTokens = GenerateRomiTokens(accountCodes, equities, "EQ");

        List<string?> tokens = new();

        if (debtTokens.Count > 0 && equityTokens.Count > 0)
        {
            if (debtTokens.Count == 2 && equityTokens.Count == 2)
            {
                tokens = Enumerable.Repeat<string?>(null, totalEquityToken).ToList();
                tokens.AddRange(equityTokens);
                tokens.AddRange(debtTokens);
            }
            else
            {
                var orderedDebtTokens = Enumerable.Repeat<string?>(null, totalDebtToken - debtTokens.Count).ToList();
                orderedDebtTokens.AddRange(debtTokens);

                var orderedEquityTokens = Enumerable.Repeat<string?>(null, totalEquityToken - equityTokens.Count).ToList();
                orderedEquityTokens.AddRange(equityTokens);

                tokens = orderedEquityTokens.Concat(orderedDebtTokens).ToList();
            }
        }
        else if (debtTokens.Count == 0 && equityTokens.Count > 0)
        {
            totalEquityToken = totalEquityToken + totalDebtToken;
            var orderedEquityTokens = Enumerable.Repeat<string?>(null, totalEquityToken - equityTokens.Count).ToList();
            orderedEquityTokens.AddRange(equityTokens);
            tokens = orderedEquityTokens;
        }
        else if (debtTokens.Count > 0 && equityTokens.Count == 0)
        {
            totalDebtToken = totalDebtToken + totalEquityToken;
            var orderedDebtTokens = Enumerable.Repeat<string?>(null, totalDebtToken - debtTokens.Count).ToList();
            orderedDebtTokens.AddRange(debtTokens);
            tokens = orderedDebtTokens;
        }

        var tokenCount = equityTokens.Count + debtTokens.Count;

        return (tokenCount, tokens);
    }

    private static List<string> GenerateRomiTokens(List<int> accountCodes, List<List<int>> groups, string prefix)
    {
        var tokens = new List<string>();
        char label = 'A';

        foreach (var group in groups)
        {
            if (accountCodes.Intersect(group).Any())
                tokens.Add($"{prefix} {label}");

            label++;
        }

        if (tokens.Count > 0)
        {
            tokens.Add($"{prefix} TOT");
        }

        return tokens;
    }

    private static async Task<(Guid, List<PeriodTrialBalance>)> GetTrialBalancesAsync(TokenType tokenType, AccountsBuilderReportingMessageDto data)
    {
        var businessType = data.ClientData?.CompanyType;
        var currentPeriod = data.ReportingPeriods.MaxBy(x => x.EndDate);
        var tokenTypeNumbers = MultiTokenHelper.GetNumbersForTokenType(tokenType, businessType);

        // For BorrowingsMaturityPY, we only want previous period data
        if (tokenType == TokenType.BorrowingsMaturityPY && data.ReportingPeriods.Count > 1)
        {
            var previousPeriod = data.ReportingPeriods.MinBy(x => x.EndDate);
            var previousPeriodTrialBalanceWithNonZeroValue = data.TrialBalanceData
                .Where(x => x.PeriodId == previousPeriod!.Id && x.Amount != 0 && tokenTypeNumbers.Contains(x.AccountCode))
                .ToList();

            return await Task.FromResult((currentPeriod!.Id, previousPeriodTrialBalanceWithNonZeroValue));
        }

        // For BorrowingsMaturityCY, we only want current period data
        if (tokenType == TokenType.BorrowingsMaturityCY)
        {
            var currentPeriodTrialBalanceWithNonZeroValue = data.TrialBalanceData
                .Where(x => x.PeriodId == currentPeriod!.Id && x.Amount != 0 && tokenTypeNumbers.Contains(x.AccountCode))
                .ToList();

            return await Task.FromResult((currentPeriod!.Id, currentPeriodTrialBalanceWithNonZeroValue));
        }

        // Default behavior for other token types
        var trialBalanceWithNonZeroValue = data.TrialBalanceData
            .Where(x => x.PeriodId == currentPeriod!.Id && x.Amount != 0 && tokenTypeNumbers.Contains(x.AccountCode))
            .ToList();

        if (data.ReportingPeriods.Count > 1 &&
            IsPreviousPeriodIncluded(data.ReportType, tokenType))
        {
            var previousPeriod = data.ReportingPeriods.MinBy(x => x.EndDate);
            var previousPeriodTrialBalanceWithNonZeroValue = data.TrialBalanceData
            .Where(x => x.PeriodId == previousPeriod!.Id && x.Amount != 0 && tokenTypeNumbers.Contains(x.AccountCode))
            .ToList();

            trialBalanceWithNonZeroValue.AddRange(previousPeriodTrialBalanceWithNonZeroValue);
        }

        return await Task.FromResult((currentPeriod!.Id, trialBalanceWithNonZeroValue));
    }

    public static bool IsPreviousPeriodIncluded(string reportType, TokenType tokenType)
    {
        var tokenTypeMap = new Dictionary<TokenType, HashSet<string>>
        {
            {
                TokenType.SocieLimitedCompany, new HashSet<string> { ReportStandardType.FRS102, ReportStandardType.IFRS }
            },
            {
                TokenType.RomiLlp, new HashSet<string> { ReportStandardType.FRS102 }
            },
            {
                TokenType.TangibleFixedAssets, new HashSet<string> { ReportStandardType.IFRS }
            },
            {
                TokenType.Investments, new HashSet<string> { ReportStandardType.IFRS }
            },
            {
                TokenType.LoansOthFinAssets, new HashSet<string> { ReportStandardType.IFRS }
            },
            {
                TokenType.TangibleFixedAssetsROU, new HashSet<string> { ReportStandardType.IFRS }
            },
            {
                TokenType.BorrowingsMaturityPY, new HashSet<string> { ReportStandardType.IFRS }
            }
        };

        var isIncluded = tokenTypeMap.ContainsKey(tokenType) &&
                         tokenTypeMap[tokenType].Contains(reportType);

        return isIncluded;
    }

    private static async Task<MultiColumnToken> AddMultiColumnToken(Guid clientId, Guid periodId, int tokenCount, TokenType tokenType, List<string?> orderedToken)
    {
        var multiColumnToken = new MultiColumnToken
        {
            ClientId = clientId,
            AccountPeriodId = periodId,
            AssignedToken = tokenType.ToString(),
            TokenCount = tokenCount
        };

        var tokenProperties = new List<Action<string?>>
        {
            value => multiColumnToken.Token1 = value,
            value => multiColumnToken.Token2 = value,
            value => multiColumnToken.Token3 = value,
            value => multiColumnToken.Token4 = value,
            value => multiColumnToken.Token5 = value,
            value => multiColumnToken.Token6 = value,
            value => multiColumnToken.Token7 = value,
            value => multiColumnToken.Token8 = value,
            value => multiColumnToken.Token9 = value
        };

        int maxTokens = Math.Min(orderedToken.Count, tokenProperties.Count);

        for (int i = 0; i < maxTokens; i++)
        {
            tokenProperties[i](string.IsNullOrEmpty(orderedToken[i]) && i == maxTokens - 1 ? "Total" : orderedToken[i]);
        }

        return await Task.FromResult(multiColumnToken);
    }

    private static async Task<List<int>> GetAccountCodes(TokenType tokenType, List<PeriodTrialBalance> trialBalances, NonTrialBalanceDataDto? nonTrialBalanceData)
    {
        var accountCodes = trialBalances.Select(x => x.AccountCode).Distinct().ToList();

        if (tokenType == TokenType.SocieLimitedCompany &&
            nonTrialBalanceData is not null &&
            nonTrialBalanceData.IsProfitLossReserveAvailable &&
            !accountCodes.Contains(968))
        {
            accountCodes.Add(968);
        }

        if (tokenType == TokenType.RomiLlp &&
            nonTrialBalanceData is not null &&
            nonTrialBalanceData.IsAllocatedProfitAvailable)
        {
            var otherReserves = new List<int> { 972, 973, 974, 999 };
            var memberCapitalDebtors = new List<int> { 985, 987, 980, 981, 997, 998 };

            if (!accountCodes.Intersect(otherReserves).Any())
                accountCodes.Add(972);

            if (!accountCodes.Intersect(memberCapitalDebtors).Any())
                accountCodes.Add(985);
        }

        if (tokenType == TokenType.BorrowingsMaturityCY || tokenType == TokenType.BorrowingsMaturityPY)
        {
            var additionalSubAccounts = new List<int>();

            foreach (var parentCode in accountCodes)
            {
                var subAccounts = GetSubAccountCodes(
                    reportType: ReportStandardType.IFRS, 
                    bussinessType: BusinessTypes.LimitedBusinessType,
                    tokenType: tokenType,
                    AccountCode: parentCode
                );
                additionalSubAccounts.AddRange(subAccounts);
            }
            accountCodes.AddRange(additionalSubAccounts);
        }

        if (tokenType == TokenType.TangibleFixedAssetsROU)
        {
            var rouCodes = trialBalances
                .Where(tb => tb.SubAccountCode is >= 51 and <= 80)
                .Select(tb => tb.AccountCode)
                .Distinct()
                .ToList();

            return await Task.FromResult(rouCodes);
        }

        return await Task.FromResult(accountCodes);
    }

    private static async Task<bool> CheckAllocatedProfitAvailable(ProfitShareDataDto profitShareData)
    {
        var isAllocatedProfitAvailable = profitShareData.UnallocatedAmount != 0 || profitShareData.ProfitShares.Any(x => x.CumulativeAmount != 0);
        return await Task.FromResult(isAllocatedProfitAvailable);
    }

    private static async Task<bool> CheckProfitLossReserve(FinancialDataDto? financialData)
    {
        var isProfitLossReserveAvailable = financialData is not null && financialData.Data.Any(x => x.ProfitLossReserve != null && x.ProfitLossReserve.Value != "0");
        return await Task.FromResult(isProfitLossReserveAvailable);
    }
}
